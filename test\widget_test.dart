// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:flutter_widget_showcase/main.dart';

void main() {
  testWidgets('Flutter Widget Showcase app test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the main title is displayed.
    expect(find.text('Flutter Widget 教学'), findsOneWidget);
    expect(find.text('欢迎来到 Flutter Widget 学习中心'), findsOneWidget);

    // Verify that some key category cards are displayed.
    expect(find.text('基础组件'), findsOneWidget);
    expect(find.text('布局组件'), findsOneWidget);

    // Verify that the grid view is present
    expect(find.byType(GridView), findsOneWidget);

    // Verify that there are multiple cards
    expect(find.byType(Card), findsWidgets);
  });
}
