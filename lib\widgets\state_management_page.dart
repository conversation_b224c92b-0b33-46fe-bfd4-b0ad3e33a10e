import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库
import 'package:provider/provider.dart'; // 导入Provider状态管理库

// 状态管理示例页面
class StateManagementPage extends StatefulWidget {
  const StateManagementPage({super.key}); // 构造函数，接收可选的key参数

  @override
  State<StateManagementPage> createState() => _StateManagementPageState();
}

class _StateManagementPageState extends State<StateManagementPage>
    with TickerProviderStateMixin {
  // 提供动画Ticker和生命周期管理

  // setState状态管理
  int _setStateCounter = 0; // setState计数器

  // 生命周期相关
  late AnimationController _animationController; // 动画控制器
  String _lifecycleStatus = '初始化'; // 生命周期状态

  @override
  void initState() {
    super.initState();
    setState(() {
      _lifecycleStatus = 'initState - 组件初始化'; // 更新生命周期状态
    });

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(seconds: 2), // 动画持续时间2秒
      vsync: this, // 垂直同步信号提供者
    );

    // 延迟更新状态，模拟异步操作
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        // 检查组件是否还在Widget树中
        setState(() {
          _lifecycleStatus = 'initState完成 - 组件已准备就绪'; // 更新生命周期状态
        });
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当依赖发生变化时调用
    // 在生产环境中，这里可以记录生命周期日志
    // print('didChangeDependencies - 依赖发生变化');
  }

  @override
  void didUpdateWidget(StateManagementPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当Widget配置发生变化时调用
    // 在生产环境中，这里可以记录生命周期日志
    // print('didUpdateWidget - Widget配置更新');
  }

  @override
  void dispose() {
    _animationController.dispose(); // 释放动画控制器资源
    setState(() {
      _lifecycleStatus = 'dispose - 组件销毁'; // 更新生命周期状态
    });
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('状态管理'), // AppBar标题
        backgroundColor:
            Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
      ), // 页面顶部导航栏
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // setState状态管理示例
          _buildStateSection(
            context,
            'setState - 基础状态管理',
            '最基本的状态管理方式，适合简单的局部状态',
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16), // 内边距16像素
                child: Column(
                  children: [
                    Text(
                      '计数器: $_setStateCounter', // 显示计数器值
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    const SizedBox(height: 16), // 垂直间距16像素
                    Row(
                      mainAxisAlignment:
                          MainAxisAlignment.spaceEvenly, // 主轴均匀分布
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _setStateCounter++; // 增加计数器
                            });
                          },
                          child: const Text('增加'), // 按钮文本
                        ),
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _setStateCounter--; // 减少计数器
                            });
                          },
                          child: const Text('减少'), // 按钮文本
                        ),
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _setStateCounter = 0; // 重置计数器
                            });
                          },
                          child: const Text('重置'), // 按钮文本
                        ),
                      ],
                    ),
                    const SizedBox(height: 12), // 垂直间距12像素
                    const Text(
                      'setState适用于单个Widget内的状态管理，当状态改变时会重新构建整个Widget',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                      textAlign: TextAlign.center, // 文本居中对齐
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // InheritedWidget状态管理示例
          _buildStateSection(
            context,
            'InheritedWidget - 数据向下传递',
            '高效的数据向下传递机制，子Widget可以访问祖先Widget的数据',
            CounterInheritedWidget(
              counter: _setStateCounter, // 传递计数器值
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16), // 内边距16像素
                  child: Column(
                    children: [
                      const Text(
                        'InheritedWidget示例:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12), // 垂直间距12像素
                      const InheritedCounterDisplay(), // 显示继承的计数器
                      const SizedBox(height: 12), // 垂直间距12像素
                      const Text(
                        'InheritedWidget允许数据在Widget树中高效传递，子Widget可以通过context访问祖先的数据',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                        textAlign: TextAlign.center, // 文本居中对齐
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // Provider状态管理示例
          _buildStateSection(
            context,
            'Provider - 全局状态管理',
            '基于InheritedWidget的状态管理解决方案，支持依赖注入',
            ChangeNotifierProvider(
              create: (context) => CounterProvider(), // 创建Provider
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16), // 内边距16像素
                  child: Column(
                    children: [
                      const Text(
                        'Provider示例:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12), // 垂直间距12像素
                      const ProviderCounterDisplay(), // 显示Provider计数器
                      const SizedBox(height: 12), // 垂直间距12像素
                      const ProviderCounterButtons(), // Provider计数器按钮
                      const SizedBox(height: 12), // 垂直间距12像素
                      const Text(
                        'Provider提供了更强大的状态管理能力，支持多种Provider类型和依赖注入',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                        textAlign: TextAlign.center, // 文本居中对齐
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // 生命周期管理示例
          _buildStateSection(
            context,
            'Widget生命周期',
            '展示Widget的生命周期方法和状态变化',
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16), // 内边距16像素
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                  children: [
                    Text(
                      '当前生命周期状态:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8), // 垂直间距8像素
                    Container(
                      width: double.infinity, // 宽度填满父容器
                      padding: const EdgeInsets.all(12), // 内边距12像素
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1), // 半透明蓝色背景
                        borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                        border: Border.all(
                          color: Colors.blue.withValues(alpha: 0.3),
                        ), // 半透明蓝色边框
                      ),
                      child: Text(
                        _lifecycleStatus, // 显示生命周期状态
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const SizedBox(height: 16), // 垂直间距16像素
                    const Text(
                      '生命周期方法说明:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8), // 垂直间距8像素
                    const Text(
                      '• initState(): 组件初始化时调用\n'
                      '• didChangeDependencies(): 依赖变化时调用\n'
                      '• build(): 构建UI时调用\n'
                      '• didUpdateWidget(): Widget配置更新时调用\n'
                      '• dispose(): 组件销毁时调用',
                      style: TextStyle(fontSize: 12, height: 1.5),
                    ),
                    const SizedBox(height: 16), // 垂直间距16像素
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _lifecycleStatus =
                              'setState调用 - 触发build方法'; // 更新生命周期状态
                        });
                        // 延迟恢复状态
                        Future.delayed(const Duration(seconds: 1), () {
                          if (mounted) {
                            setState(() {
                              _lifecycleStatus = 'build完成 - UI已更新'; // 更新生命周期状态
                            });
                          }
                        });
                      },
                      child: const Text('触发setState'), // 按钮文本
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // ValueNotifier状态管理示例
          _buildStateSection(
            context,
            'ValueNotifier - 轻量级状态管理',
            '轻量级的状态管理方案，适合简单的响应式数据',
            const ValueNotifierExample(), // ValueNotifier示例组件
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 构建状态管理展示区域的辅助方法
  Widget _buildStateSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    Widget content, // 要展示的内容Widget
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            content, // 显示传入的内容Widget
          ],
        ),
      ),
    );
  }
}

// InheritedWidget示例
class CounterInheritedWidget extends InheritedWidget {
  final int counter; // 计数器值

  const CounterInheritedWidget({
    super.key,
    required this.counter,
    required super.child,
  }); // 构造函数

  // 提供静态方法供子Widget访问数据
  static CounterInheritedWidget? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<CounterInheritedWidget>();
  }

  @override
  bool updateShouldNotify(CounterInheritedWidget oldWidget) {
    return counter != oldWidget.counter; // 当计数器值改变时通知子Widget更新
  }
}

// InheritedWidget计数器显示组件
class InheritedCounterDisplay extends StatelessWidget {
  const InheritedCounterDisplay({super.key}); // 构造函数

  @override
  Widget build(BuildContext context) {
    final inheritedWidget = CounterInheritedWidget.of(
      context,
    ); // 获取InheritedWidget数据
    final counter = inheritedWidget?.counter ?? 0; // 获取计数器值，默认为0

    return Container(
      padding: const EdgeInsets.all(12), // 内边距12像素
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1), // 半透明绿色背景
        borderRadius: BorderRadius.circular(8), // 圆角半径8像素
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.3),
        ), // 半透明绿色边框
      ),
      child: Text(
        'InheritedWidget计数器: $counter', // 显示计数器值
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      ),
    );
  }
}

// Provider状态管理类
class CounterProvider extends ChangeNotifier {
  int _counter = 0; // 私有计数器

  int get counter => _counter; // 获取计数器值

  void increment() {
    _counter++; // 增加计数器
    notifyListeners(); // 通知监听者
  }

  void decrement() {
    _counter--; // 减少计数器
    notifyListeners(); // 通知监听者
  }

  void reset() {
    _counter = 0; // 重置计数器
    notifyListeners(); // 通知监听者
  }
}

// Provider计数器显示组件
class ProviderCounterDisplay extends StatelessWidget {
  const ProviderCounterDisplay({super.key}); // 构造函数

  @override
  Widget build(BuildContext context) {
    return Consumer<CounterProvider>(
      builder: (context, counterProvider, child) {
        return Container(
          padding: const EdgeInsets.all(12), // 内边距12像素
          decoration: BoxDecoration(
            color: Colors.purple.withValues(alpha: 0.1), // 半透明紫色背景
            borderRadius: BorderRadius.circular(8), // 圆角半径8像素
            border: Border.all(
              color: Colors.purple.withValues(alpha: 0.3),
            ), // 半透明紫色边框
          ),
          child: Text(
            'Provider计数器: ${counterProvider.counter}', // 显示计数器值
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        );
      },
    );
  }
}

// Provider计数器按钮组件
class ProviderCounterButtons extends StatelessWidget {
  const ProviderCounterButtons({super.key}); // 构造函数

  @override
  Widget build(BuildContext context) {
    return Consumer<CounterProvider>(
      builder: (context, counterProvider, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly, // 主轴均匀分布
          children: [
            ElevatedButton(
              onPressed: counterProvider.increment, // 增加计数器
              child: const Text('增加'), // 按钮文本
            ),
            ElevatedButton(
              onPressed: counterProvider.decrement, // 减少计数器
              child: const Text('减少'), // 按钮文本
            ),
            ElevatedButton(
              onPressed: counterProvider.reset, // 重置计数器
              child: const Text('重置'), // 按钮文本
            ),
          ],
        );
      },
    );
  }
}

// ValueNotifier示例组件
class ValueNotifierExample extends StatefulWidget {
  const ValueNotifierExample({super.key}); // 构造函数

  @override
  State<ValueNotifierExample> createState() => _ValueNotifierExampleState();
}

class _ValueNotifierExampleState extends State<ValueNotifierExample> {
  final ValueNotifier<int> _counterNotifier = ValueNotifier<int>(
    0,
  ); // ValueNotifier计数器

  @override
  void dispose() {
    _counterNotifier.dispose(); // 释放ValueNotifier资源
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16), // 内边距16像素
        child: Column(
          children: [
            const Text(
              'ValueNotifier示例:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12), // 垂直间距12像素
            ValueListenableBuilder<int>(
              valueListenable: _counterNotifier, // 监听ValueNotifier
              builder: (context, value, child) {
                return Container(
                  padding: const EdgeInsets.all(12), // 内边距12像素
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1), // 半透明橙色背景
                    borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                    border: Border.all(
                      color: Colors.orange.withValues(alpha: 0.3),
                    ), // 半透明橙色边框
                  ),
                  child: Text(
                    'ValueNotifier计数器: $value', // 显示计数器值
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 12), // 垂直间距12像素
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly, // 主轴均匀分布
              children: [
                ElevatedButton(
                  onPressed: () => _counterNotifier.value++, // 增加计数器
                  child: const Text('增加'), // 按钮文本
                ),
                ElevatedButton(
                  onPressed: () => _counterNotifier.value--, // 减少计数器
                  child: const Text('减少'), // 按钮文本
                ),
                ElevatedButton(
                  onPressed: () => _counterNotifier.value = 0, // 重置计数器
                  child: const Text('重置'), // 按钮文本
                ),
              ],
            ),
            const SizedBox(height: 12), // 垂直间距12像素
            const Text(
              'ValueNotifier是一个轻量级的状态管理方案，适合管理单个值的变化',
              style: TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center, // 文本居中对齐
            ),
          ],
        ),
      ),
    );
  }
}
