import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库
import 'package:dio/dio.dart'; // 导入Dio网络请求库
import 'dart:convert'; // 导入JSON转换库

// 网络请求示例页面
class NetworkPage extends StatefulWidget {
  const NetworkPage({super.key}); // 构造函数，接收可选的key参数

  @override
  State<NetworkPage> createState() => _NetworkPageState();
}

class _NetworkPageState extends State<NetworkPage> {
  final Dio _dio = Dio(); // Dio实例
  bool _isLoading = false; // 是否正在加载
  String _responseData = ''; // 响应数据
  String _errorMessage = ''; // 错误信息
  List<dynamic> _userList = []; // 用户列表
  Map<String, dynamic>? _postData; // POST请求数据

  @override
  void initState() {
    super.initState();
    _initializeDio(); // 初始化Dio配置
  }

  // 初始化Dio配置
  void _initializeDio() {
    _dio.options.baseUrl = 'https://jsonplaceholder.typicode.com'; // 设置基础URL
    _dio.options.connectTimeout = const Duration(seconds: 5); // 连接超时5秒
    _dio.options.receiveTimeout = const Duration(seconds: 3); // 接收超时3秒

    // 添加请求拦截器
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // 在生产环境中，这里可以记录请求日志
          // print('请求: ${options.method} ${options.uri}');
          handler.next(options); // 继续请求
        },
        onResponse: (response, handler) {
          // 在生产环境中，这里可以记录响应日志
          // print('响应: ${response.statusCode}');
          handler.next(response); // 继续响应
        },
        onError: (error, handler) {
          // 在生产环境中，这里可以记录错误日志
          // print('错误: ${error.message}');
          handler.next(error); // 继续错误处理
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('网络请求'), // AppBar标题
        backgroundColor:
            Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
      ), // 页面顶部导航栏
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // GET请求示例
          _buildNetworkSection(
            context,
            'GET请求 - 获取数据',
            '使用GET方法从服务器获取数据',
            Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed:
                            _isLoading
                                ? null
                                : _performGetRequest, // 如果正在加载则禁用按钮
                        icon: const Icon(Icons.download), // 下载图标
                        label: const Text('获取单个用户'), // 按钮文本
                      ),
                    ),
                    const SizedBox(width: 12), // 水平间距12像素
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed:
                            _isLoading
                                ? null
                                : _performGetListRequest, // 如果正在加载则禁用按钮
                        icon: const Icon(Icons.list), // 列表图标
                        label: const Text('获取用户列表'), // 按钮文本
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16), // 垂直间距16像素
                if (_isLoading)
                  const Column(
                    children: [
                      CircularProgressIndicator(), // 加载指示器
                      SizedBox(height: 8), // 垂直间距8像素
                      Text('正在加载...'), // 加载提示文本
                    ],
                  ),
                if (_responseData.isNotEmpty && !_isLoading)
                  Container(
                    width: double.infinity, // 宽度填满父容器
                    padding: const EdgeInsets.all(12), // 内边距12像素
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1), // 半透明绿色背景
                      borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                      border: Border.all(
                        color: Colors.green.withValues(alpha: 0.3),
                      ), // 半透明绿色边框
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                      children: [
                        const Text(
                          '响应数据:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8), // 垂直间距8像素
                        Text(
                          _responseData, // 显示响应数据
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                if (_userList.isNotEmpty && !_isLoading)
                  Container(
                    width: double.infinity, // 宽度填满父容器
                    padding: const EdgeInsets.all(12), // 内边距12像素
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1), // 半透明蓝色背景
                      borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                      border: Border.all(
                        color: Colors.blue.withValues(alpha: 0.3),
                      ), // 半透明蓝色边框
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                      children: [
                        Text(
                          '用户列表 (${_userList.length}个用户):',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8), // 垂直间距8像素
                        ...(_userList
                            .take(3)
                            .map(
                              (user) => Padding(
                                padding: const EdgeInsets.only(
                                  bottom: 4,
                                ), // 底部外边距4像素
                                child: Text(
                                  '${user['id']}. ${user['name']} (${user['email']})', // 显示用户信息
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ),
                            )),
                        if (_userList.length > 3)
                          Text(
                            '... 还有${_userList.length - 3}个用户',
                            style: const TextStyle(
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // POST请求示例
          _buildNetworkSection(
            context,
            'POST请求 - 提交数据',
            '使用POST方法向服务器提交数据',
            Column(
              children: [
                ElevatedButton.icon(
                  onPressed:
                      _isLoading ? null : _performPostRequest, // 如果正在加载则禁用按钮
                  icon: const Icon(Icons.upload), // 上传图标
                  label: const Text('创建新用户'), // 按钮文本
                ),
                const SizedBox(height: 16), // 垂直间距16像素
                if (_postData != null && !_isLoading)
                  Container(
                    width: double.infinity, // 宽度填满父容器
                    padding: const EdgeInsets.all(12), // 内边距12像素
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.1), // 半透明紫色背景
                      borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                      border: Border.all(
                        color: Colors.purple.withValues(alpha: 0.3),
                      ), // 半透明紫色边框
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                      children: [
                        const Text(
                          'POST响应数据:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8), // 垂直间距8像素
                        Text(
                          'ID: ${_postData!['id']}\n'
                          '标题: ${_postData!['title']}\n'
                          '内容: ${_postData!['body']}\n'
                          '用户ID: ${_postData!['userId']}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // 错误处理示例
          _buildNetworkSection(
            context,
            '错误处理 - 网络异常',
            '演示网络请求错误处理和重试机制',
            Column(
              children: [
                ElevatedButton.icon(
                  onPressed:
                      _isLoading ? null : _performErrorRequest, // 如果正在加载则禁用按钮
                  icon: const Icon(Icons.error), // 错误图标
                  label: const Text('模拟网络错误'), // 按钮文本
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red, // 红色背景
                    foregroundColor: Colors.white, // 白色文字
                  ),
                ),
                const SizedBox(height: 16), // 垂直间距16像素
                if (_errorMessage.isNotEmpty && !_isLoading)
                  Container(
                    width: double.infinity, // 宽度填满父容器
                    padding: const EdgeInsets.all(12), // 内边距12像素
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1), // 半透明红色背景
                      borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                      border: Border.all(
                        color: Colors.red.withValues(alpha: 0.3),
                      ), // 半透明红色边框
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                      children: [
                        const Row(
                          children: [
                            Icon(
                              Icons.error,
                              color: Colors.red,
                              size: 20,
                            ), // 错误图标
                            SizedBox(width: 8), // 水平间距8像素
                            Text(
                              '网络错误:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8), // 垂直间距8像素
                        Text(
                          _errorMessage, // 显示错误信息
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // 网络配置信息
          _buildNetworkSection(
            context,
            'Dio配置信息',
            '当前网络请求库的配置参数',
            Container(
              width: double.infinity, // 宽度填满父容器
              padding: const EdgeInsets.all(12), // 内边距12像素
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1), // 半透明灰色背景
                borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.3),
                ), // 半透明灰色边框
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                children: [
                  const Text(
                    'Dio配置:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8), // 垂直间距8像素
                  Text(
                    '基础URL: ${_dio.options.baseUrl}\n'
                    '连接超时: ${_dio.options.connectTimeout?.inSeconds}秒\n'
                    '接收超时: ${_dio.options.receiveTimeout?.inSeconds}秒\n'
                    '拦截器数量: ${_dio.interceptors.length}个',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 执行GET请求获取单个用户
  Future<void> _performGetRequest() async {
    setState(() {
      _isLoading = true; // 设置加载状态
      _responseData = ''; // 清空响应数据
      _errorMessage = ''; // 清空错误信息
      _userList = []; // 清空用户列表
      _postData = null; // 清空POST数据
    });

    try {
      final response = await _dio.get('/users/1'); // 发送GET请求获取用户1的信息
      setState(() {
        _responseData = const JsonEncoder.withIndent(
          '  ',
        ).convert(response.data); // 格式化JSON数据
        _isLoading = false; // 取消加载状态
      });
    } catch (e) {
      setState(() {
        _errorMessage = '获取用户信息失败: $e'; // 设置错误信息
        _isLoading = false; // 取消加载状态
      });
    }
  }

  // 执行GET请求获取用户列表
  Future<void> _performGetListRequest() async {
    setState(() {
      _isLoading = true; // 设置加载状态
      _responseData = ''; // 清空响应数据
      _errorMessage = ''; // 清空错误信息
      _userList = []; // 清空用户列表
      _postData = null; // 清空POST数据
    });

    try {
      final response = await _dio.get('/users'); // 发送GET请求获取用户列表
      setState(() {
        _userList = response.data; // 设置用户列表
        _isLoading = false; // 取消加载状态
      });
    } catch (e) {
      setState(() {
        _errorMessage = '获取用户列表失败: $e'; // 设置错误信息
        _isLoading = false; // 取消加载状态
      });
    }
  }

  // 执行POST请求创建新用户
  Future<void> _performPostRequest() async {
    setState(() {
      _isLoading = true; // 设置加载状态
      _responseData = ''; // 清空响应数据
      _errorMessage = ''; // 清空错误信息
      _userList = []; // 清空用户列表
      _postData = null; // 清空POST数据
    });

    try {
      final response = await _dio.post(
        '/posts', // POST请求端点
        data: {
          'title': 'Flutter网络请求示例', // 标题
          'body': '这是一个使用Dio库发送POST请求的示例', // 内容
          'userId': 1, // 用户ID
        },
      );
      setState(() {
        _postData = response.data; // 设置POST响应数据
        _isLoading = false; // 取消加载状态
      });
    } catch (e) {
      setState(() {
        _errorMessage = '创建帖子失败: $e'; // 设置错误信息
        _isLoading = false; // 取消加载状态
      });
    }
  }

  // 执行错误请求演示错误处理
  Future<void> _performErrorRequest() async {
    setState(() {
      _isLoading = true; // 设置加载状态
      _responseData = ''; // 清空响应数据
      _errorMessage = ''; // 清空错误信息
      _userList = []; // 清空用户列表
      _postData = null; // 清空POST数据
    });

    try {
      // 故意请求一个不存在的端点来触发错误
      await _dio.get('/nonexistent-endpoint');
    } catch (e) {
      setState(() {
        if (e is DioException) {
          // 处理Dio特定的异常
          switch (e.type) {
            case DioExceptionType.connectionTimeout:
              _errorMessage = '连接超时，请检查网络连接';
              break;
            case DioExceptionType.receiveTimeout:
              _errorMessage = '接收数据超时';
              break;
            case DioExceptionType.badResponse:
              _errorMessage = '服务器响应错误: ${e.response?.statusCode}';
              break;
            case DioExceptionType.cancel:
              _errorMessage = '请求被取消';
              break;
            case DioExceptionType.unknown:
              _errorMessage = '未知网络错误: ${e.message}';
              break;
            default:
              _errorMessage = 'Dio错误: ${e.message}';
          }
        } else {
          _errorMessage = '网络请求失败: $e'; // 设置通用错误信息
        }
        _isLoading = false; // 取消加载状态
      });
    }
  }

  // 构建网络请求展示区域的辅助方法
  Widget _buildNetworkSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    Widget content, // 要展示的内容Widget
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            content, // 显示传入的内容Widget
          ],
        ),
      ),
    );
  }
}
