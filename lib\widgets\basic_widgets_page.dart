import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库

// 基础Widget展示页面
class BasicWidgetsPage extends StatelessWidget {
  const BasicWidgetsPage({super.key}); // 构造函数，接收可选的key参数

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('基础组件'), // AppBar标题
        backgroundColor:
            Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
      ), // 页面顶部导航栏
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // Text Widget 示例
          _buildWidgetSection(
            context,
            'Text Widget - 文本组件',
            '用于显示文本内容，支持样式设置',
            [
              const Text(
                '这是普通文本', // 文本内容
                style: TextStyle(fontSize: 16), // 文本样式：字体大小16
              ),
              const Text(
                '这是加粗文本',
                style: TextStyle(
                  fontSize: 18, // 字体大小18
                  fontWeight: FontWeight.bold, // 字体加粗
                  color: Colors.blue, // 文字颜色蓝色
                ),
              ),
              const Text(
                '这是斜体文本',  
                style: TextStyle(
                  fontSize: 16,
                  fontStyle: FontStyle.italic, // 斜体样式
                  decoration: TextDecoration.underline, // 下划线装饰
                ),
              ),
              RichText(
                text: TextSpan(
                  text: "Hello ", // 富文本的第一部分
                  style: const TextStyle(
                    fontSize: 20,
                    color: Colors.red,
                  ), // 红色文字样式
                  children: [
                    TextSpan(
                      text: "Flutter!", // 富文本的第二部分
                      style: const TextStyle(
                        fontSize: 20,
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                      ), // 蓝色加粗文字样式
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // Container Widget 示例
          _buildWidgetSection(
            context,
            'Container Widget - 容器组件',
            '提供装饰、定位、尺寸约束的容器',
            [
              Container(
                width: 100, // 容器宽度100像素
                height: 100, // 容器高度100像素
                color: Colors.red, // 背景颜色红色
                child: const Center(
                  child: Text(
                    '红色容器',
                    style: TextStyle(color: Colors.white), // 白色文字
                  ),
                ),
              ),
              Container(
                width: 120, // 容器宽度120像素
                height: 80, // 容器高度80像素
                decoration: BoxDecoration(
                  color: Colors.blue, // 背景颜色蓝色
                  borderRadius: BorderRadius.circular(15), // 圆角半径15像素
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.grey, // 阴影颜色灰色
                      offset: Offset(2, 2), // 阴影偏移量x:2, y:2
                      blurRadius: 5, // 阴影模糊半径5像素
                    ),
                  ],
                ), // 容器装饰：颜色、圆角、阴影
                child: const Center(
                  child: Text('圆角阴影', style: TextStyle(color: Colors.white)),
                ),
              ),
              Container(
                width: 150, // 容器宽度150像素
                height: 60, // 容器高度60像素
                margin: const EdgeInsets.all(10), // 外边距四周各10像素
                padding: const EdgeInsets.all(15), // 内边距四周各15像素
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colors.purple, Colors.pink], // 渐变色：紫色到粉色
                    begin: Alignment.topLeft, // 渐变开始位置：左上角
                    end: Alignment.bottomRight, // 渐变结束位置：右下角
                  ),
                  border: Border.all(
                    color: Colors.black, // 边框颜色黑色
                    width: 2, // 边框宽度2像素
                  ),
                ),
                child: const Text(
                  '渐变边框',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // Icon Widget 示例
          _buildWidgetSection(
            context,
            'Icon Widget - 图标组件',
            '显示Material Design图标',
            [
              const Icon(
                Icons.home, // 首页图标
                size: 30, // 图标大小30像素
                color: Colors.blue, // 图标颜色蓝色
              ),
              const Icon(
                Icons.favorite, // 喜欢图标
                size: 40, // 图标大小40像素
                color: Colors.red, // 图标颜色红色
              ),
              const Icon(
                Icons.star, // 星星图标
                size: 50, // 图标大小50像素
                color: Colors.orange, // 图标颜色橙色
              ),
            ],
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // Image Widget 示例
          _buildWidgetSection(
            context,
            'Image Widget - 图片组件',
            '显示图片，支持网络图片、本地图片等',
            [
              Container(
                width: 100, // 容器宽度100像素
                height: 100, // 容器高度100像素
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey), // 灰色边框
                ),
                child: const Icon(
                  Icons.image, // 图片占位图标
                  size: 50,
                  color: Colors.grey,
                ),
              ),
              Container(
                width: 120, // 容器宽度120像素
                height: 80, // 容器高度80像素
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(10), // 圆角边框
                ),
                child: const ClipRRect(
                  borderRadius: BorderRadius.all(Radius.circular(10)), // 裁剪圆角
                  child: Icon(
                    Icons.photo, // 照片图标
                    size: 40,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 构建Widget展示区域的辅助方法
  Widget _buildWidgetSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    List<Widget> widgets, // 要展示的Widget列表
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            Wrap(
              spacing: 16, // 水平间距16像素
              runSpacing: 16, // 垂直间距16像素
              children: widgets, // 显示传入的Widget列表
            ),
          ],
        ),
      ),
    );
  }
}
