import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// GetX教程页面
/// 包含状态管理、路由管理、依赖注入等功能演示
class GetXPage extends StatelessWidget {
  const GetXPage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 5,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('GetX 状态管理教程'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          bottom: const TabBar(
            isScrollable: true,
            tabs: [
              Tab(text: '状态管理', icon: Icon(Icons.settings)),
              Tab(text: '路由管理', icon: Icon(Icons.navigation)),
              Tab(text: '依赖注入', icon: Icon(Icons.extension)),
              Tab(text: '主题控制', icon: Icon(Icons.palette)),
              Tab(text: '国际化', icon: Icon(Icons.language)),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            _StateManagementTab(),
            _RouteManagementTab(),
            _DependencyInjectionTab(),
            _ThemeControlTab(),
            _InternationalizationTab(),
          ],
        ),
      ),
    );
  }
}

/// 状态管理演示
class _StateManagementTab extends StatelessWidget {
  const _StateManagementTab();

  @override
  Widget build(BuildContext context) {
    // 初始化控制器
    Get.put(CounterController());

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 说明卡片
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'GetX 状态管理',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'GetX 提供了简单而强大的状态管理解决方案：\n'
                    '• 响应式编程（Reactive Programming）\n'
                    '• 简单状态管理（Simple State Manager）\n'
                    '• 无需 StatefulWidget\n'
                    '• 自动内存管理',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // 计数器示例
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Text(
                    '响应式计数器示例',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 使用 GetX 响应式状态
                  GetX<CounterController>(
                    builder: (controller) {
                      return Column(
                        children: [
                          Text(
                            '计数: ${controller.count.value}',
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              ElevatedButton(
                                onPressed: controller.increment,
                                child: const Text('增加'),
                              ),
                              ElevatedButton(
                                onPressed: controller.decrement,
                                child: const Text('减少'),
                              ),
                              ElevatedButton(
                                onPressed: controller.reset,
                                child: const Text('重置'),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Obx 示例
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Text(
                    'Obx 简化写法',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 使用 Obx 更简洁的写法
                  Obx(() {
                    final controller = Get.find<CounterController>();
                    return Column(
                      children: [
                        Text(
                          '双倍计数: ${controller.doubleCount}',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: (controller.count.value % 10) / 10,
                          backgroundColor: Colors.grey.shade300,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '进度: ${controller.count.value % 10}/10',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 路由管理演示
class _RouteManagementTab extends StatelessWidget {
  const _RouteManagementTab();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'GetX 路由管理',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'GetX 提供了强大的路由管理功能：\n'
                    '• 无需 context 的导航\n'
                    '• 命名路由\n'
                    '• 路由参数传递\n'
                    '• 中间件支持\n'
                    '• 嵌套路由',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // 路由示例按钮
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    '路由导航示例',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  ElevatedButton(
                    onPressed: () {
                      Get.to(() => const DetailPage(title: '普通导航'));
                    },
                    child: const Text('Get.to() - 普通导航'),
                  ),
                  const SizedBox(height: 8),

                  ElevatedButton(
                    onPressed: () {
                      Get.toNamed('/detail', arguments: {'title': '命名路由'});
                    },
                    child: const Text('Get.toNamed() - 命名路由'),
                  ),
                  const SizedBox(height: 8),

                  ElevatedButton(
                    onPressed: () {
                      Get.off(() => const DetailPage(title: '替换当前页面'));
                    },
                    child: const Text('Get.off() - 替换当前页面'),
                  ),
                  const SizedBox(height: 8),

                  ElevatedButton(
                    onPressed: () {
                      Get.offAll(() => const DetailPage(title: '清空路由栈'));
                    },
                    child: const Text('Get.offAll() - 清空路由栈'),
                  ),
                  const SizedBox(height: 8),

                  ElevatedButton(
                    onPressed: () {
                      Get.dialog(
                        AlertDialog(
                          title: const Text('GetX Dialog'),
                          content: const Text('这是使用 Get.dialog() 显示的对话框'),
                          actions: [
                            TextButton(
                              onPressed: () => Get.back(),
                              child: const Text('关闭'),
                            ),
                          ],
                        ),
                      );
                    },
                    child: const Text('Get.dialog() - 显示对话框'),
                  ),
                  const SizedBox(height: 8),

                  ElevatedButton(
                    onPressed: () {
                      Get.snackbar(
                        'GetX Snackbar',
                        '这是使用 Get.snackbar() 显示的消息',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.blue,
                        colorText: Colors.white,
                      );
                    },
                    child: const Text('Get.snackbar() - 显示消息'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 依赖注入演示
class _DependencyInjectionTab extends StatelessWidget {
  const _DependencyInjectionTab();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'GetX 依赖注入',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'GetX 提供了简单的依赖注入系统：\n'
                    '• Get.put() - 立即创建实例\n'
                    '• Get.lazyPut() - 懒加载创建\n'
                    '• Get.putAsync() - 异步创建\n'
                    '• Get.create() - 每次获取都创建新实例\n'
                    '• 自动内存管理',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    '依赖注入示例',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  ElevatedButton(
                    onPressed: () {
                      // 注入服务
                      Get.put(ApiService());
                      Get.snackbar('成功', '已注入 ApiService');
                    },
                    child: const Text('Get.put() - 注入服务'),
                  ),
                  const SizedBox(height: 8),

                  ElevatedButton(
                    onPressed: () {
                      try {
                        final service = Get.find<ApiService>();
                        Get.snackbar('成功', '找到服务: ${service.toString()}');
                      } catch (e) {
                        Get.snackbar('错误', '未找到 ApiService，请先注入');
                      }
                    },
                    child: const Text('Get.find() - 查找服务'),
                  ),
                  const SizedBox(height: 8),

                  ElevatedButton(
                    onPressed: () {
                      Get.delete<ApiService>();
                      Get.snackbar('成功', '已删除 ApiService');
                    },
                    child: const Text('Get.delete() - 删除服务'),
                  ),
                  const SizedBox(height: 8),

                  ElevatedButton(
                    onPressed: () {
                      Get.lazyPut(() => LazyService());
                      Get.snackbar('成功', '已懒加载注入 LazyService');
                    },
                    child: const Text('Get.lazyPut() - 懒加载注入'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 主题控制演示
class _ThemeControlTab extends StatelessWidget {
  const _ThemeControlTab();

  @override
  Widget build(BuildContext context) {
    // 初始化主题控制器
    Get.put(ThemeController());

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 说明卡片
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'GetX 主题控制',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'GetX 提供了简单的主题管理功能：\n'
                    '• 响应式主题切换\n'
                    '• 深色/浅色模式\n'
                    '• 自定义主题颜色\n'
                    '• 主题状态持久化',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // 主题切换示例
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    '主题模式切换',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 当前主题显示
                  Obx(() {
                    final controller = Get.find<ThemeController>();
                    return Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            controller.isDarkMode.value
                                ? Icons.dark_mode
                                : Icons.light_mode,
                            size: 48,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            controller.isDarkMode.value ? '深色模式' : '浅色模式',
                            style: Theme.of(context).textTheme.titleLarge
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '当前主题: ${controller.currentThemeName}',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    );
                  }),

                  const SizedBox(height: 16),

                  // 主题切换按钮
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Get.find<ThemeController>().toggleTheme();
                          },
                          icon: const Icon(Icons.brightness_6),
                          label: const Text('切换模式'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Get.find<ThemeController>().setLightTheme();
                          },
                          icon: const Icon(Icons.light_mode),
                          label: const Text('浅色'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Get.find<ThemeController>().setDarkTheme();
                          },
                          icon: const Icon(Icons.dark_mode),
                          label: const Text('深色'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // 主题颜色切换
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    '主题颜色切换',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 颜色选择器
                  Obx(() {
                    final controller = Get.find<ThemeController>();
                    return Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          ThemeController.themeColors.entries.map((entry) {
                            final isSelected =
                                controller.currentColorName.value == entry.key;
                            return GestureDetector(
                              onTap:
                                  () => controller.changeThemeColor(entry.key),
                              child: Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: entry.value,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color:
                                        isSelected
                                            ? Theme.of(
                                              context,
                                            ).colorScheme.primary
                                            : Colors.grey.shade300,
                                    width: isSelected ? 3 : 1,
                                  ),
                                ),
                                child:
                                    isSelected
                                        ? Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 24,
                                        )
                                        : null,
                              ),
                            );
                          }).toList(),
                    );
                  }),

                  const SizedBox(height: 16),

                  Obx(() {
                    final controller = Get.find<ThemeController>();
                    return Text(
                      '当前颜色: ${controller.currentColorName.value}',
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 国际化演示
class _InternationalizationTab extends StatelessWidget {
  const _InternationalizationTab();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'GetX 国际化',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'GetX 提供了简单的国际化解决方案：\n'
                    '• 无需 context 的翻译\n'
                    '• 动态语言切换\n'
                    '• 参数化翻译\n'
                    '• 回退语言支持',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    '语言切换示例',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Text(
                    'hello'.tr, // 使用 .tr 进行翻译
                    style: Theme.of(context).textTheme.headlineMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          Get.updateLocale(const Locale('zh', 'CN'));
                        },
                        child: const Text('中文'),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          Get.updateLocale(const Locale('en', 'US'));
                        },
                        child: const Text('English'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  Text(
                    'welcome_message'.trParams({'name': 'GetX'}),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 计数器控制器
class CounterController extends GetxController {
  // 响应式变量
  final count = 0.obs;

  // 计算属性
  int get doubleCount => count.value * 2;

  // 方法
  void increment() => count.value++;
  void decrement() => count.value--;
  void reset() => count.value = 0;
}

/// 示例服务类
class ApiService extends GetxService {
  void fetchData() {
    // 模拟数据获取
    debugPrint('Fetching data...');
  }
}

/// 懒加载服务类
class LazyService extends GetxService {
  LazyService() {
    debugPrint('LazyService created');
  }
}

/// 详情页面
class DetailPage extends StatelessWidget {
  final String title;

  const DetailPage({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(title, style: Theme.of(context).textTheme.headlineMedium),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => Get.back(),
              child: const Text('返回'),
            ),
          ],
        ),
      ),
    );
  }
}

/// 主题控制器
class ThemeController extends GetxController {
  // 响应式变量
  final isDarkMode = false.obs;
  final currentColorName = 'deepPurple'.obs;

  // 主题颜色映射
  static const Map<String, Color> themeColors = {
    'deepPurple': Colors.deepPurple,
    'blue': Colors.blue,
    'green': Colors.green,
    'orange': Colors.orange,
    'red': Colors.red,
    'teal': Colors.teal,
    'pink': Colors.pink,
    'indigo': Colors.indigo,
  };

  // 计算属性
  String get currentThemeName => isDarkMode.value ? '深色主题' : '浅色主题';

  Color get currentColor =>
      themeColors[currentColorName.value] ?? Colors.deepPurple;

  ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: currentColor,
      brightness: Brightness.light,
    ),
    fontFamily: "DinPro",
  );

  ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: currentColor,
      brightness: Brightness.dark,
    ),
    fontFamily: "DinPro",
  );

  // 方法
  void toggleTheme() {
    isDarkMode.value = !isDarkMode.value;
    _updateAppTheme();
  }

  void setLightTheme() {
    isDarkMode.value = false;
    _updateAppTheme();
  }

  void setDarkTheme() {
    isDarkMode.value = true;
    _updateAppTheme();
  }

  void changeThemeColor(String colorName) {
    if (themeColors.containsKey(colorName)) {
      currentColorName.value = colorName;
      _updateAppTheme();
    }
  }

  void _updateAppTheme() {
    Get.changeTheme(isDarkMode.value ? darkTheme : lightTheme);
  }

  @override
  void onInit() {
    super.onInit();
    // 初始化时应用默认主题
    _updateAppTheme();
  }
}

/// 翻译类
class Messages extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    'en_US': {'hello': 'Hello', 'welcome_message': 'Welcome to @name!'},
    'zh_CN': {'hello': '你好', 'welcome_message': '欢迎使用 @name！'},
  };
}
