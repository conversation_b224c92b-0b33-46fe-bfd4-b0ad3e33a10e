import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库
import 'package:shared_preferences/shared_preferences.dart'; // 导入SharedPreferences本地存储库

// 本地存储示例页面
class StoragePage extends StatefulWidget {
  const StoragePage({super.key}); // 构造函数，接收可选的key参数

  @override
  State<StoragePage> createState() => _StoragePageState();
}

class _StoragePageState extends State<StoragePage> {
  // 文本输入控制器
  final TextEditingController _stringController = TextEditingController(); // 字符串输入控制器
  final TextEditingController _intController = TextEditingController(); // 整数输入控制器
  final TextEditingController _doubleController = TextEditingController(); // 浮点数输入控制器
  
  // 存储的数据
  String _storedString = ''; // 存储的字符串
  int _storedInt = 0; // 存储的整数
  double _storedDouble = 0.0; // 存储的浮点数
  bool _storedBool = false; // 存储的布尔值
  List<String> _storedStringList = []; // 存储的字符串列表
  
  // 用户设置
  String _selectedTheme = 'light'; // 选中的主题
  bool _notificationsEnabled = true; // 是否启用通知
  double _fontSize = 16.0; // 字体大小
  String _language = 'zh'; // 语言设置

  @override
  void initState() {
    super.initState();
    _loadAllData(); // 加载所有数据
  }

  @override
  void dispose() {
    _stringController.dispose(); // 释放控制器资源
    _intController.dispose();
    _doubleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('本地存储'), // AppBar标题
        backgroundColor: Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_sweep), // 清除图标
            onPressed: _clearAllData, // 清除所有数据
            tooltip: '清除所有数据', // 工具提示
          ),
        ],
      ), // 页面顶部导航栏
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // 基础数据类型存储
          _buildStorageSection(
            context,
            '基础数据类型存储',
            '存储和读取字符串、整数、浮点数、布尔值等基础类型',
            Column(
              children: [
                // 字符串存储
                _buildDataTypeCard(
                  '字符串 (String)',
                  TextField(
                    controller: _stringController, // 绑定控制器
                    decoration: const InputDecoration(
                      labelText: '输入字符串', // 标签文本
                      border: OutlineInputBorder(), // 边框样式
                    ),
                  ),
                  Text('存储的值: $_storedString'), // 显示存储的值
                  [
                    ElevatedButton(
                      onPressed: () => _saveString(_stringController.text), // 保存字符串
                      child: const Text('保存'), // 按钮文本
                    ),
                    ElevatedButton(
                      onPressed: _loadString, // 加载字符串
                      child: const Text('读取'), // 按钮文本
                    ),
                  ],
                ),
                const SizedBox(height: 12), // 垂直间距12像素
                
                // 整数存储
                _buildDataTypeCard(
                  '整数 (int)',
                  TextField(
                    controller: _intController, // 绑定控制器
                    keyboardType: TextInputType.number, // 数字键盘
                    decoration: const InputDecoration(
                      labelText: '输入整数', // 标签文本
                      border: OutlineInputBorder(), // 边框样式
                    ),
                  ),
                  Text('存储的值: $_storedInt'), // 显示存储的值
                  [
                    ElevatedButton(
                      onPressed: () => _saveInt(int.tryParse(_intController.text) ?? 0), // 保存整数
                      child: const Text('保存'), // 按钮文本
                    ),
                    ElevatedButton(
                      onPressed: _loadInt, // 加载整数
                      child: const Text('读取'), // 按钮文本
                    ),
                  ],
                ),
                const SizedBox(height: 12), // 垂直间距12像素
                
                // 浮点数存储
                _buildDataTypeCard(
                  '浮点数 (double)',
                  TextField(
                    controller: _doubleController, // 绑定控制器
                    keyboardType: const TextInputType.numberWithOptions(decimal: true), // 带小数点的数字键盘
                    decoration: const InputDecoration(
                      labelText: '输入浮点数', // 标签文本
                      border: OutlineInputBorder(), // 边框样式
                    ),
                  ),
                  Text('存储的值: ${_storedDouble.toStringAsFixed(2)}'), // 显示存储的值，保留2位小数
                  [
                    ElevatedButton(
                      onPressed: () => _saveDouble(double.tryParse(_doubleController.text) ?? 0.0), // 保存浮点数
                      child: const Text('保存'), // 按钮文本
                    ),
                    ElevatedButton(
                      onPressed: _loadDouble, // 加载浮点数
                      child: const Text('读取'), // 按钮文本
                    ),
                  ],
                ),
                const SizedBox(height: 12), // 垂直间距12像素
                
                // 布尔值存储
                _buildDataTypeCard(
                  '布尔值 (bool)',
                  SwitchListTile(
                    title: const Text('开关状态'), // 开关标题
                    value: _storedBool, // 开关当前值
                    onChanged: (bool value) {
                      _saveBool(value); // 保存布尔值
                    },
                  ),
                  Text('存储的值: $_storedBool'), // 显示存储的值
                  [
                    ElevatedButton(
                      onPressed: _loadBool, // 加载布尔值
                      child: const Text('读取'), // 按钮文本
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // 字符串列表存储
          _buildStorageSection(
            context,
            '字符串列表存储',
            '存储和管理字符串列表数据',
            Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: const InputDecoration(
                          labelText: '添加到列表', // 标签文本
                          border: OutlineInputBorder(), // 边框样式
                        ),
                        onSubmitted: (value) {
                          if (value.isNotEmpty) {
                            _addToStringList(value); // 添加到字符串列表
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 12), // 水平间距12像素
                    ElevatedButton(
                      onPressed: _loadStringList, // 加载字符串列表
                      child: const Text('读取列表'), // 按钮文本
                    ),
                  ],
                ),
                const SizedBox(height: 16), // 垂直间距16像素
                Container(
                  width: double.infinity, // 宽度填满父容器
                  padding: const EdgeInsets.all(12), // 内边距12像素
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1), // 半透明蓝色背景
                    borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                    border: Border.all(color: Colors.blue.withValues(alpha: 0.3)), // 半透明蓝色边框
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                    children: [
                      Text(
                        '存储的列表 (${_storedStringList.length}项):',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8), // 垂直间距8像素
                      if (_storedStringList.isEmpty)
                        const Text('列表为空', style: TextStyle(fontStyle: FontStyle.italic))
                      else
                        ...(_storedStringList.asMap().entries.map((entry) {
                          int index = entry.key;
                          String value = entry.value;
                          return Row(
                            children: [
                              Expanded(
                                child: Text('${index + 1}. $value'), // 显示列表项
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete, size: 16), // 删除图标
                                onPressed: () => _removeFromStringList(index), // 删除列表项
                              ),
                            ],
                          );
                        })),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // 用户设置存储
          _buildStorageSection(
            context,
            '用户设置存储',
            '模拟应用设置的存储和管理',
            Column(
              children: [
                // 主题设置
                ListTile(
                  title: const Text('主题设置'), // 标题
                  subtitle: Text('当前: $_selectedTheme'), // 副标题
                  trailing: DropdownButton<String>(
                    value: _selectedTheme, // 当前选中值
                    items: const [
                      DropdownMenuItem(value: 'light', child: Text('浅色')),
                      DropdownMenuItem(value: 'dark', child: Text('深色')),
                      DropdownMenuItem(value: 'auto', child: Text('自动')),
                    ],
                    onChanged: (String? value) {
                      if (value != null) {
                        _saveTheme(value); // 保存主题设置
                      }
                    },
                  ),
                ),
                
                // 通知设置
                SwitchListTile(
                  title: const Text('推送通知'), // 标题
                  subtitle: const Text('是否接收推送通知'), // 副标题
                  value: _notificationsEnabled, // 开关当前值
                  onChanged: (bool value) {
                    _saveNotificationSetting(value); // 保存通知设置
                  },
                ),
                
                // 字体大小设置
                ListTile(
                  title: const Text('字体大小'), // 标题
                  subtitle: Text('当前: ${_fontSize.toStringAsFixed(0)}px'), // 副标题
                  trailing: SizedBox(
                    width: 150, // 宽度150像素
                    child: Slider(
                      value: _fontSize, // 滑块当前值
                      min: 12.0, // 最小值
                      max: 24.0, // 最大值
                      divisions: 12, // 分割数量
                      label: _fontSize.toStringAsFixed(0), // 滑块标签
                      onChanged: (double value) {
                        _saveFontSize(value); // 保存字体大小
                      },
                    ),
                  ),
                ),
                
                // 语言设置
                ListTile(
                  title: const Text('语言设置'), // 标题
                  subtitle: Text('当前: ${_language == 'zh' ? '中文' : 'English'}'), // 副标题
                  trailing: DropdownButton<String>(
                    value: _language, // 当前选中值
                    items: const [
                      DropdownMenuItem(value: 'zh', child: Text('中文')),
                      DropdownMenuItem(value: 'en', child: Text('English')),
                    ],
                    onChanged: (String? value) {
                      if (value != null) {
                        _saveLanguage(value); // 保存语言设置
                      }
                    },
                  ),
                ),
                
                const SizedBox(height: 16), // 垂直间距16像素
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _loadUserSettings, // 加载用户设置
                        child: const Text('重新加载设置'), // 按钮文本
                      ),
                    ),
                    const SizedBox(width: 12), // 水平间距12像素
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _resetUserSettings, // 重置用户设置
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange, // 橙色背景
                          foregroundColor: Colors.white, // 白色文字
                        ),
                        child: const Text('重置设置'), // 按钮文本
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 构建数据类型卡片的辅助方法
  Widget _buildDataTypeCard(
    String title, // 卡片标题
    Widget inputWidget, // 输入组件
    Widget displayWidget, // 显示组件
    List<Widget> buttons, // 按钮列表
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12), // 内边距12像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            inputWidget, // 显示输入组件
            const SizedBox(height: 8), // 垂直间距8像素
            displayWidget, // 显示显示组件
            const SizedBox(height: 8), // 垂直间距8像素
            Row(
              children: buttons.map((button) => Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4), // 水平内边距4像素
                  child: button, // 显示按钮
                ),
              )).toList(),
            ),
          ],
        ),
      ),
    );
  }

  // 保存字符串
  Future<void> _saveString(String value) async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    await prefs.setString('stored_string', value); // 保存字符串
    setState(() {
      _storedString = value; // 更新状态
    });
    _showSnackBar('字符串已保存'); // 显示提示
  }

  // 加载字符串
  Future<void> _loadString() async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    setState(() {
      _storedString = prefs.getString('stored_string') ?? ''; // 加载字符串，默认为空
    });
    _showSnackBar('字符串已加载'); // 显示提示
  }

  // 保存整数
  Future<void> _saveInt(int value) async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    await prefs.setInt('stored_int', value); // 保存整数
    setState(() {
      _storedInt = value; // 更新状态
    });
    _showSnackBar('整数已保存'); // 显示提示
  }

  // 加载整数
  Future<void> _loadInt() async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    setState(() {
      _storedInt = prefs.getInt('stored_int') ?? 0; // 加载整数，默认为0
    });
    _showSnackBar('整数已加载'); // 显示提示
  }

  // 保存浮点数
  Future<void> _saveDouble(double value) async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    await prefs.setDouble('stored_double', value); // 保存浮点数
    setState(() {
      _storedDouble = value; // 更新状态
    });
    _showSnackBar('浮点数已保存'); // 显示提示
  }

  // 加载浮点数
  Future<void> _loadDouble() async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    setState(() {
      _storedDouble = prefs.getDouble('stored_double') ?? 0.0; // 加载浮点数，默认为0.0
    });
    _showSnackBar('浮点数已加载'); // 显示提示
  }

  // 保存布尔值
  Future<void> _saveBool(bool value) async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    await prefs.setBool('stored_bool', value); // 保存布尔值
    setState(() {
      _storedBool = value; // 更新状态
    });
    _showSnackBar('布尔值已保存'); // 显示提示
  }

  // 加载布尔值
  Future<void> _loadBool() async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    setState(() {
      _storedBool = prefs.getBool('stored_bool') ?? false; // 加载布尔值，默认为false
    });
    _showSnackBar('布尔值已加载'); // 显示提示
  }

  // 添加到字符串列表
  Future<void> _addToStringList(String value) async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    List<String> currentList = prefs.getStringList('stored_string_list') ?? []; // 获取当前列表
    currentList.add(value); // 添加新值
    await prefs.setStringList('stored_string_list', currentList); // 保存列表
    setState(() {
      _storedStringList = currentList; // 更新状态
    });
    _showSnackBar('已添加到列表'); // 显示提示
  }

  // 从字符串列表中删除
  Future<void> _removeFromStringList(int index) async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    List<String> currentList = List.from(_storedStringList); // 复制当前列表
    currentList.removeAt(index); // 删除指定索引的项
    await prefs.setStringList('stored_string_list', currentList); // 保存列表
    setState(() {
      _storedStringList = currentList; // 更新状态
    });
    _showSnackBar('已从列表中删除'); // 显示提示
  }

  // 加载字符串列表
  Future<void> _loadStringList() async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    setState(() {
      _storedStringList = prefs.getStringList('stored_string_list') ?? []; // 加载列表，默认为空列表
    });
    _showSnackBar('列表已加载'); // 显示提示
  }

  // 保存主题设置
  Future<void> _saveTheme(String theme) async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    await prefs.setString('theme', theme); // 保存主题
    setState(() {
      _selectedTheme = theme; // 更新状态
    });
    _showSnackBar('主题设置已保存'); // 显示提示
  }

  // 保存通知设置
  Future<void> _saveNotificationSetting(bool enabled) async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    await prefs.setBool('notifications_enabled', enabled); // 保存通知设置
    setState(() {
      _notificationsEnabled = enabled; // 更新状态
    });
    _showSnackBar('通知设置已保存'); // 显示提示
  }

  // 保存字体大小
  Future<void> _saveFontSize(double size) async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    await prefs.setDouble('font_size', size); // 保存字体大小
    setState(() {
      _fontSize = size; // 更新状态
    });
    _showSnackBar('字体大小已保存'); // 显示提示
  }

  // 保存语言设置
  Future<void> _saveLanguage(String language) async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    await prefs.setString('language', language); // 保存语言
    setState(() {
      _language = language; // 更新状态
    });
    _showSnackBar('语言设置已保存'); // 显示提示
  }

  // 加载用户设置
  Future<void> _loadUserSettings() async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    setState(() {
      _selectedTheme = prefs.getString('theme') ?? 'light'; // 加载主题，默认为浅色
      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true; // 加载通知设置，默认为启用
      _fontSize = prefs.getDouble('font_size') ?? 16.0; // 加载字体大小，默认为16
      _language = prefs.getString('language') ?? 'zh'; // 加载语言，默认为中文
    });
    _showSnackBar('用户设置已加载'); // 显示提示
  }

  // 重置用户设置
  Future<void> _resetUserSettings() async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    await prefs.remove('theme'); // 删除主题设置
    await prefs.remove('notifications_enabled'); // 删除通知设置
    await prefs.remove('font_size'); // 删除字体大小设置
    await prefs.remove('language'); // 删除语言设置
    setState(() {
      _selectedTheme = 'light'; // 重置为默认值
      _notificationsEnabled = true;
      _fontSize = 16.0;
      _language = 'zh';
    });
    _showSnackBar('用户设置已重置'); // 显示提示
  }

  // 加载所有数据
  Future<void> _loadAllData() async {
    await _loadString(); // 加载字符串
    await _loadInt(); // 加载整数
    await _loadDouble(); // 加载浮点数
    await _loadBool(); // 加载布尔值
    await _loadStringList(); // 加载字符串列表
    await _loadUserSettings(); // 加载用户设置
  }

  // 清除所有数据
  Future<void> _clearAllData() async {
    final prefs = await SharedPreferences.getInstance(); // 获取SharedPreferences实例
    await prefs.clear(); // 清除所有数据
    setState(() {
      _storedString = ''; // 重置所有状态
      _storedInt = 0;
      _storedDouble = 0.0;
      _storedBool = false;
      _storedStringList = [];
      _selectedTheme = 'light';
      _notificationsEnabled = true;
      _fontSize = 16.0;
      _language = 'zh';
    });
    _showSnackBar('所有数据已清除'); // 显示提示
  }

  // 显示SnackBar的辅助方法
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message), // SnackBar显示的消息
        duration: const Duration(seconds: 2), // 显示时长2秒
      ),
    );
  }

  // 构建存储展示区域的辅助方法
  Widget _buildStorageSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    Widget content, // 要展示的内容Widget
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            content, // 显示传入的内容Widget
          ],
        ),
      ),
    );
  }
}
