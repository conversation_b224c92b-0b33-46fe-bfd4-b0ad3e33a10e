import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

/// 瀑布流教程页面
/// 包含正确用法和错误用法的对比示例
class WaterfallPage extends StatefulWidget {
  const WaterfallPage({super.key});

  @override
  State<WaterfallPage> createState() => _WaterfallPageState();
}

class _WaterfallPageState extends State<WaterfallPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('瀑布流布局教程'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '正确用法', icon: Icon(Icons.check_circle)),
            Tab(text: '错误用法', icon: Icon(Icons.error)),
            Tab(text: '性能优化', icon: Icon(Icons.speed)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          _CorrectWaterfallTab(),
          _IncorrectWaterfallTab(),
          _OptimizedWaterfallTab(),
        ],
      ),
    );
  }
}

/// 正确的瀑布流用法
class _CorrectWaterfallTab extends StatelessWidget {
  const _CorrectWaterfallTab();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 说明文本
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb, color: Colors.green),
                      SizedBox(width: 8),
                      Text(
                        '正确用法要点',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• 使用 flutter_staggered_grid_view 包\n'
                    '• 合理设置 crossAxisCount（列数）\n'
                    '• 使用 StaggeredTile.fit() 自适应高度\n'
                    '• 为图片设置合适的缓存策略',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 瀑布流示例
          Expanded(
            child: MasonryGridView.count(
              crossAxisCount: 2, // 2列布局
              itemCount: 20,
              itemBuilder: (BuildContext context, int index) {
                return _buildWaterfallItem(context, index, true);
              },
              mainAxisSpacing: 8.0, // 主轴间距
              crossAxisSpacing: 8.0, // 交叉轴间距
            ),
          ),
        ],
      ),
    );
  }
}

/// 错误的瀑布流用法
class _IncorrectWaterfallTab extends StatelessWidget {
  const _IncorrectWaterfallTab();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 错误说明
          Card(
            color: Colors.red.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning, color: Colors.red),
                      SizedBox(width: 8),
                      Text(
                        '常见错误',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• 使用固定高度的 GridView（无法实现瀑布流效果）\n'
                    '• 不设置合适的 childAspectRatio\n'
                    '• 在 Column 中直接使用 GridView 不设置高度\n'
                    '• 过多的列数导致内容过小',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 错误示例：使用普通 GridView
          Text(
            '错误示例：使用普通 GridView（高度固定）',
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Expanded(
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 8.0,
                mainAxisSpacing: 8.0,
                childAspectRatio: 0.8, // 固定宽高比，无法实现瀑布流
              ),
              itemCount: 20,
              itemBuilder: (context, index) {
                return _buildWaterfallItem(context, index, false);
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// 性能优化的瀑布流
class _OptimizedWaterfallTab extends StatelessWidget {
  const _OptimizedWaterfallTab();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 优化说明
          Card(
            color: Colors.blue.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.speed, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        '性能优化技巧',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• 使用 AutomaticKeepAliveClientMixin 保持状态\n'
                    '• 图片懒加载和缓存\n'
                    '• 合理的 itemExtent 设置\n'
                    '• 使用 RepaintBoundary 减少重绘',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 优化后的瀑布流
          Expanded(
            child: MasonryGridView.count(
              crossAxisCount: 2,
              itemCount: 50, // 更多数据测试性能
              itemBuilder: (BuildContext context, int index) {
                return RepaintBoundary(
                  // 减少重绘
                  child: _OptimizedWaterfallItem(index: index),
                );
              },
              mainAxisSpacing: 8.0,
              crossAxisSpacing: 8.0,
            ),
          ),
        ],
      ),
    );
  }
}

/// 优化的瀑布流项目
class _OptimizedWaterfallItem extends StatefulWidget {
  final int index;

  const _OptimizedWaterfallItem({required this.index});

  @override
  State<_OptimizedWaterfallItem> createState() =>
      _OptimizedWaterfallItemState();
}

class _OptimizedWaterfallItemState extends State<_OptimizedWaterfallItem>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true; // 保持状态，避免重复构建

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用，用于 AutomaticKeepAliveClientMixin

    return _buildWaterfallItem(context, widget.index, true);
  }
}

/// 构建瀑布流项目的通用方法
Widget _buildWaterfallItem(BuildContext context, int index, bool isCorrect) {
  // 模拟不同高度的内容
  final heights = [120.0, 180.0, 150.0, 200.0, 160.0, 140.0, 190.0, 170.0];
  final height = heights[index % heights.length];

  final colors = [
    Colors.red.shade100,
    Colors.blue.shade100,
    Colors.green.shade100,
    Colors.orange.shade100,
    Colors.purple.shade100,
    Colors.teal.shade100,
    Colors.pink.shade100,
    Colors.indigo.shade100,
  ];

  return Card(
    elevation: 2,
    child: Container(
      height: isCorrect ? height : null, // 正确用法使用动态高度
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colors[index % colors.length],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 模拟图片
          Container(
            height: 80,
            width: double.infinity,
            decoration: BoxDecoration(
              color: colors[index % colors.length].withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(Icons.image, size: 40, color: Colors.grey.shade600),
          ),
          SizedBox(height: 8),
          Text(
            '项目 ${index + 1}',
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 4),
          Text(
            '这是第${index + 1}个瀑布流项目的描述内容。'
            '${isCorrect ? "使用正确的瀑布流布局" : "使用固定高度布局"}',
            style: Theme.of(context).textTheme.bodySmall,
            maxLines: isCorrect ? null : 3,
            overflow: isCorrect ? null : TextOverflow.ellipsis,
          ),
          if (isCorrect && index % 3 == 0) ...[
            SizedBox(height: 8),
            Text(
              '额外的内容让高度更加多样化，这样才能体现瀑布流的效果。',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
            ),
          ],
        ],
      ),
    ),
  );
}
