# Flutter Widget 教学项目

一个全面的Flutter Widget展示和学习应用，专为学习Flutter开发而设计。

## 项目简介

这个项目是一个完整的Flutter教学应用，包含了Flutter中各种常用Widget的详细示例和中文注释。每个Widget都有详细的属性说明，帮助初学者更好地理解Flutter开发。

## 功能特色

- 📱 **完整的Widget展示** - 涵盖基础、布局、交互、滚动等各类Widget
- 📝 **详细的中文注释** - 每个属性都有详细的中文说明
- 🎨 **美观的界面设计** - 使用Material 3设计规范
- 🔍 **分类清晰** - 按功能分类，便于学习和查找
- 💡 **实用的示例** - 每个Widget都有实际的使用示例

## 项目结构

```
lib/
├── main.dart                          # 应用入口和主页面
└── widgets/                           # Widget展示页面
    ├── basic_widgets_page.dart        # 基础组件展示
    ├── layout_widgets_page.dart       # 布局组件展示
    ├── interactive_widgets_page.dart  # 交互组件展示
    ├── scrollable_widgets_page.dart   # 滚动组件展示
    ├── animation_widgets_page.dart    # 动画组件展示
    ├── theme_widgets_page.dart        # 主题样式展示
    ├── advanced_layout_page.dart      # 高级布局展示
    ├── cupertino_widgets_page.dart    # iOS风格组件展示
    ├── data_widgets_page.dart         # 数据展示组件
    ├── navigation_widgets_page.dart   # 导航组件展示
    ├── state_management_page.dart     # 状态管理示例
    ├── network_page.dart              # 网络请求示例
    └── storage_page.dart              # 本地存储示例
```

## Widget分类

### 1. 基础组件 (Basic Widgets)
- **Text** - 文本显示组件，支持富文本
- **Container** - 容器组件，提供装饰、定位、尺寸约束
- **Icon** - 图标组件
- **Image** - 图片显示组件

### 2. 布局组件 (Layout Widgets)
- **Row** - 水平布局组件
- **Column** - 垂直布局组件
- **Stack** - 层叠布局组件
- **Flex** - 弹性布局组件
- **Wrap** - 流式布局组件

### 3. 交互组件 (Interactive Widgets)
- **Button系列** - ElevatedButton、OutlinedButton、TextButton、IconButton
- **TextField** - 文本输入框
- **Switch** - 开关组件
- **Checkbox** - 复选框组件
- **Radio** - 单选按钮组件
- **Slider** - 滑块组件

### 4. 滚动组件 (Scrollable Widgets)
- **ListView** - 列表视图
- **GridView** - 网格视图
- **PageView** - 页面视图
- **SingleChildScrollView** - 单子组件滚动视图
- **CustomScrollView** - 自定义滚动视图

### 5. 动画组件 (Animation Widgets)
- **旋转动画** - RotationTransition
- **缩放动画** - ScaleTransition
- **淡入淡出** - FadeTransition
- **滑动动画** - SlideTransition
- **Hero动画** - 页面间共享元素动画

### 6. 主题样式 (Theme & Style)
- **主题切换** - 深色/浅色模式
- **颜色方案** - Material 3颜色系统
- **文字样式** - 各种文字主题
- **Material/Cupertino** - 双平台风格对比

### 7. 高级布局 (Advanced Layout)
- **线性布局** - 权重分配的线性排列
- **弹性布局** - FlexBox布局系统
- **流式布局** - 自动换行的标签布局
- **层叠布局** - 绝对定位的层叠效果
- **表格布局** - Table数据展示

### 8. iOS风格组件 (Cupertino Widgets)
- **Cupertino按钮** - iOS风格按钮
- **Cupertino输入框** - iOS风格文本输入
- **Cupertino控件** - 开关、滑块、分段控件
- **Cupertino选择器** - 日期选择器、滚轮选择器

### 9. 数据展示 (Data Display)
- **DataTable** - 可排序的数据表格
- **Calendar** - 功能丰富的日历组件
- **Form** - 完整的表单验证系统

### 10. 导航组件 (Navigation)
- **AppBar** - 应用顶部导航栏
- **TabBar** - 标签页导航
- **BottomNavigationBar** - 底部导航栏
- **Drawer** - 侧边抽屉导航
- **NavigationRail** - 侧边导航栏
- **路由导航** - 页面跳转和动画

### 11. 状态管理 (State Management)
- **setState** - 基础状态管理
- **InheritedWidget** - 数据向下传递
- **Provider** - 全局状态管理
- **ValueNotifier** - 轻量级状态管理
- **生命周期** - Widget生命周期演示

### 12. 网络请求 (Network)
- **Dio库** - 强大的HTTP客户端
- **GET请求** - 获取数据示例
- **POST请求** - 提交数据示例
- **错误处理** - 网络异常处理
- **拦截器** - 请求响应拦截

### 13. 本地存储 (Local Storage)
- **SharedPreferences** - 键值对存储
- **基础数据类型** - String、int、double、bool
- **列表存储** - StringList存储
- **用户设置** - 应用配置管理

## 主要依赖

- **dio**: HTTP网络请求库
- **shared_preferences**: 本地键值对存储
- **provider**: 状态管理解决方案
- **table_calendar**: 日历组件
- **card_swiper**: 轮播图组件
- **flutter_localizations**: 国际化支持

## 运行项目

1. 确保已安装Flutter SDK
2. 克隆或下载项目到本地
3. 在项目根目录运行：
   ```bash
   flutter pub get
   flutter run
   ```

## 学习建议

1. **从基础开始** - 先学习基础组件，理解Flutter的基本概念
2. **动手实践** - 运行项目，查看每个Widget的实际效果
3. **阅读注释** - 仔细阅读代码中的中文注释，理解每个属性的作用
4. **修改尝试** - 尝试修改代码中的参数，观察界面变化
5. **举一反三** - 基于示例代码，尝试创建自己的Widget组合

## 贡献

欢迎提交Issue和Pull Request来完善这个教学项目！

## 许可证

本项目采用MIT许可证。
